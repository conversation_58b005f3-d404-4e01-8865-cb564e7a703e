from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from views.forms import SetupForm

views_bp = Blueprint("views", __name__, template_folder="templates")

@views_bp.route("/setup", methods=["GET", "POST"])
def setup():
    form = SetupForm()

    if form.validate_on_submit():
        items = []
        item_names = request.form.getlist("item_name")
        quantities = request.form.getlist("quantity")

        for name, qty in zip(item_names, quantities):
            if name.strip() and qty.strip():
                quantity = int(qty)  
                items.append({"name": name.strip(), "quantity": quantity})

        if items:
            flash(f"Transaction created with {len(items)} items!", "success")
            return jsonify(items)

    return render_template("setup.html", form=form)