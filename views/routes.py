import logging
from datetime import datetime
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from wtforms.validators import NumberRange
from views.forms import SetupForm, DrawForm
from models import db, Admin, Stock, Item

views_bp = Blueprint("views", __name__, template_folder="templates")

@views_bp.route("/setup", methods=["GET", "POST"])
def setup():
    form = SetupForm()

    if form.validate_on_submit():
        items = []
        item_names = request.form.getlist("item_name")
        quantities = request.form.getlist("quantity")
        description = request.form.get("description")

        for name, qty in zip(item_names, quantities):
            if name.strip() and qty.strip():
                quantity = int(qty)  
                items.append({"name": name.strip(), "quantity": quantity})

        def upd_stock(admin_nric, items, description):
            admin = Admin.query.filter_by(nric=admin_nric).first()

            stock = Stock(admin_id=admin.id,
                          created_at=datetime.now(),
                          description=description)

            try:
                db.session.add(stock)
                db.session.commit()
            except Exception as e:
                current_app.logger.error(f"Error creating stock: {e}")
                return False

            for item in items:
                item = Item(name=item["name"],
                            quantity=item["quantity"],
                            stock_id=stock.id)
                
                try:
                    db.session.add(stock)
                    db.session.commit()
                except Exception as e:
                    current_app.logger.error(f"Error creating stock: {e}")
                    return False
                
                db.session.add(item)
                db.session.commit()   

            return stock.id

        stock_id = upd_stock('TXXXX736J', items, description)
        if stock_id:
            current_app.logger.info(f"Stock created with {len(items)} items")
            return redirect(url_for("views.dashboard", stock_id=stock_id))

    return render_template("setup.html", form=form)

@views_bp.route("/dashboard/<int:stock_id>", methods=["GET", "POST"])
def dashboard(stock_id):
    stock = Stock.query.filter_by(id=stock_id).first()

    items = []
    for item in stock.items:
        items.append({
            "name": item.name,
            "quantity": item.quantity,
            "on_loan": 0,
            "in_stock": item.quantity,
            "total": item.quantity
        })

    # Calculate summary statistics
    total_items = len(items)
    total_in_stock = sum(item["in_stock"] for item in items)
    total_on_loan = sum(item["on_loan"] for item in items)

    draw_form = DrawForm()
    draw_form.item_name.choices = [(item["name"], f"{item['name']} ({item['in_stock']} available)") for item in items]

    return render_template("dashboard.html",
                           inventory_items=items,
                           total_items=total_items,
                           total_in_stock=total_in_stock,
                           total_on_loan=total_on_loan,
                           draw_form=draw_form)



