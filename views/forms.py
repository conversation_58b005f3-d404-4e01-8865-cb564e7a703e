from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, IntegerField, SelectField
from wtforms.validators import <PERSON>Required, NumberRange, Length, ValidationError
from models import Item

class ValidateItemQuantity:
    """Dynamic validator that checks quantity against selected item's available stock"""

    def __init__(self, message=None):
        if not message:
            message = 'Insufficient stock for the selected item.'
        self.message = message

    def __call__(self, form, field):
        # if not field.data or field.data <= 0:
        #     raise ValidationError('Quantity must be greater than 0.')

        # item_name = form.item_name.data
        # if not item_name:
        #     raise ValidationError('Please select an item first.')

        # # Find the most recent stock entry for this item
        # item = Item.query.filter_by(name=item_name).order_by(Item.id.desc()).first()

        # if not item:
        #     raise ValidationError(f'Item "{item_name}" not found in inventory.')

        # # Check if requested quantity exceeds available stock
        # if field.data > item.quantity:
        #     raise ValidationError(f'Only {item.quantity} available in stock.')
        item_name = form.item_name.data
        if not item_name:
            raise ValidationError('Please select an item first.')
        

class SetupForm(FlaskForm):
    description = StringField("Stock description", validators=[DataRequired()])
    item_name = StringField("Item Name", validators=[DataRequired()])
    quantity = IntegerField("Quantity", validators=[DataRequired(), NumberRange(min=1)])

class DrawForm(FlaskForm):
    item_name = SelectField("Item Name", validators=[DataRequired()])
    quantity = IntegerField("Quantity", validators=[DataRequired(), NumberRange(min=1), ValidateItemQuantity()])