from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, IntegerField
from wtforms.validators import DataRequired, NumberRange, Length

class SetupForm(FlaskForm):
    description = StringField("Stock description", validators=[DataRequired()])
    item_name = StringField("Item Name", validators=[DataRequired()])
    quantity = IntegerField("Quantity", validators=[DataRequired(), NumberRange(min=1)])