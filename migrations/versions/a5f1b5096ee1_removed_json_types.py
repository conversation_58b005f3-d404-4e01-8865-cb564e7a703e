"""removed json types

Revision ID: a5f1b5096ee1
Revises: 1de685675aa2
Create Date: 2025-07-05 11:53:17.856434

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a5f1b5096ee1'
down_revision = '1de685675aa2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('event', schema=None) as batch_op:
        batch_op.alter_column('items',
               existing_type=sa.TEXT(),
               type_=sa.String(length=1000),
               existing_nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('event', schema=None) as batch_op:
        batch_op.alter_column('items',
               existing_type=sa.String(length=1000),
               type_=sa.TEXT(),
               existing_nullable=False)

    # ### end Alembic commands ###
