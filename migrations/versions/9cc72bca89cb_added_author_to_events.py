"""added author to events

Revision ID: 9cc72bca89cb
Revises: a5f1b5096ee1
Create Date: 2025-07-05 12:01:26.076822

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9cc72bca89cb'
down_revision = 'a5f1b5096ee1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('event', schema=None) as batch_op:
        batch_op.alter_column('author',
               existing_type=sa.NUMERIC(),
               type_=sa.String(length=256),
               nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('event', schema=None) as batch_op:
        batch_op.alter_column('author',
               existing_type=sa.String(length=256),
               type_=sa.NUMERIC(),
               nullable=True)

    # ### end Alembic commands ###
