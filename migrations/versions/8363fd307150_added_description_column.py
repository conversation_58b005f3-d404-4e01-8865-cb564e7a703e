"""added description column

Revision ID: 8363fd307150
Revises: 2bddacc41261
Create Date: 2025-07-04 17:46:51.186449

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8363fd307150'
down_revision = '2bddacc41261'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stock', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.String(length=500), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stock', schema=None) as batch_op:
        batch_op.drop_column('description')

    # ### end Alembic commands ###
