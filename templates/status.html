<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Status - Store Inventory</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .user-row {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .user-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .balance-zero {
            background-color: #d4edda !important;
            border-color: #c3e6cb !important;
        }
        .balance-outstanding {
            background-color: #fff3cd !important;
            border-color: #ffeaa7 !important;
        }
        .event-details {
            display: none;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .event-badge {
            font-size: 0.75rem;
        }
    </style>
</head>

<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>User Status Overview</h2>
                    <a href="{{ url_for('views.dashboard', stock_id=stock_id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Users</h5>
                        <small class="text-muted">
                            <span class="badge bg-success me-2">Green</span> No outstanding items
                            <span class="badge bg-warning">Yellow</span> Has outstanding items
                        </small>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">User</th>
                                        <th scope="col">Outstanding Items</th>
                                        <th scope="col">Total Events</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    {% set user_balance = balances[user.name] %}
                                    {% set user_events = events[user.name] %}
                                    {% set has_outstanding = user_balance and user_balance.values()|sum > 0 %}
                                    <tr class="user-row {% if has_outstanding %}balance-outstanding{% else %}balance-zero{% endif %}"
                                        data-user-id="{{ user.id }}" data-user-name="{{ user.name }}">
                                        <td>
                                            <strong>{{ user.name }}</strong>
                                        </td>
                                        <td>
                                            {% if has_outstanding %}
                                                {% for item_name, quantity in user_balance.items() %}
                                                    {% if quantity > 0 %}
                                                        <span class="badge bg-warning me-1">{{ item_name }}: {{ quantity }}</span>
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-success">None</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ user_events|length }}</span>
                                        </td>
                                        <td>
                                            {% if has_outstanding %}
                                                <span class="badge bg-warning">Outstanding</span>
                                            {% else %}
                                                <span class="badge bg-success">Clear</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary toggle-events"
                                                    data-user-id="{{ user.id }}">
                                                <i class="bi bi-chevron-down"></i> View Events
                                            </button>
                                        </td>
                                    </tr>
                                    <tr class="event-details" id="events-{{ user.id }}">
                                        <td colspan="5">
                                            <div class="p-3">
                                                <h6>Event History for {{ user.name }}</h6>
                                                {% if user_events %}
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-striped">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th scope="col">Date & Time</th>
                                                                    <th scope="col">Type</th>
                                                                    <th scope="col">Items</th>
                                                                    <th scope="col">Remarks</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {% for event in user_events|reverse %}
                                                                <tr>
                                                                    <td>
                                                                        <small class="text-muted">
                                                                            {{ (event.timestamp + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M') }}
                                                                        </small>
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge event-badge
                                                                            {% if event.event_type == 'draw' %}bg-danger
                                                                            {% elif event.event_type == 'send' %}bg-success
                                                                            {% else %}bg-info{% endif %}">
                                                                            {{ event.event_type.title() }}
                                                                        </span>
                                                                    </td>
                                                                    <td>
                                                                        {% set event_items = event.items|from_json %}
                                                                        {% for item_name, quantity in event_items.items() %}
                                                                            <span class="badge bg-light text-dark me-1 mb-1">
                                                                                {{ item_name }}: {{ quantity }}
                                                                            </span>
                                                                        {% endfor %}
                                                                    </td>
                                                                    <td>
                                                                        {{ event.remarks }}
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                {% else %}
                                                    <p class="text-muted mb-0">No events recorded for this user.</p>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle user row clicks to toggle event details
            document.querySelectorAll('.user-row').forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking the button
                    if (e.target.closest('.toggle-events')) return;

                    const userId = this.dataset.userId;
                    toggleEventDetails(userId);
                });
            });

            // Handle toggle button clicks
            document.querySelectorAll('.toggle-events').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const userId = this.dataset.userId;
                    toggleEventDetails(userId);
                });
            });

            function toggleEventDetails(userId) {
                const eventRow = document.getElementById(`events-${userId}`);
                const toggleButton = document.querySelector(`[data-user-id="${userId}"].toggle-events`);
                const icon = toggleButton.querySelector('i');

                if (eventRow.style.display === 'none' || eventRow.style.display === '') {
                    eventRow.style.display = 'table-row';
                    icon.className = 'bi bi-chevron-up';
                    toggleButton.innerHTML = '<i class="bi bi-chevron-up"></i> Hide Events';
                } else {
                    eventRow.style.display = 'none';
                    icon.className = 'bi bi-chevron-down';
                    toggleButton.innerHTML = '<i class="bi bi-chevron-down"></i> View Events';
                }
            }
        });
    </script>
</body>
</html>