<div class="modal fade" id="drawModal" tabindex="-1" aria-labelledby="drawModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="drawModalLabel">Draw Items from Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="drawForm" method="POST">
                <div class="modal-header">
                    <input type="text" class="form-control" name="author" placeholder="Rank and Name" required>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Item Name</th>
                                <th scope="col">Quantity</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody id="item-container">
                            <tr class="item-row">
                                <td>
                                    <select class="form-select" name="item_name" required>
                                        <option value="" disabled selected>Select an item</option>
                                        {% for item in inventory_items %}
                                        <option value="{{ item.name }}">{{ item.name }} ({{ item.in_stock }} available)</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <input type="number" name="quantity" class="form-control" required>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-item" style="display: none;">Remove</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-outline-primary" id="add-item">Add Item</button>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Draw Items</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    function addItem() {
        const container = document.getElementById("item-container");
        const newRow = document.createElement("tr");
        newRow.className = "item-row";
        newRow.innerHTML = `
            <td>
                <select class="form-select" name="item_name" required>
                    <option value="" disabled selected>Select an item</option>
                    {% for item in inventory_items %}
                    <option value="{{ item.name }}">{{ item.name }} ({{ item.in_stock }} available)</option>
                    {% endfor %}
                </select>
            </td>
            <td>
                <input type="number" name="quantity" class="form-control" required>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm remove-item" style="display: none;">Remove</button>
            </td>
        `;
        container.appendChild(newRow);
        updateRemoveButtons();
    }

    function updateRemoveButtons() {
        const rows = document.querySelectorAll(".item-row");
        const removeButton = document.querySelectorAll(".remove-item");
        removeButton.forEach((btn, index) => {
            if (rows.length > 1) {
                btn.style.display = 'block';
                btn.onclick = function() {
                    rows[index].remove();
                    updateRemoveButtons();
                };
            } else {
                btn.style.display = 'none';
            }
        });
    }

    document.getElementById('add-item').addEventListener('click', addItem);
</script>
