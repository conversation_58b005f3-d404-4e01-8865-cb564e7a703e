<div class="modal fade" id="sendModal" tabindex="-1" aria-labelledby="sendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendModalLabel">Send Items to Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sendForm" method="POST">
                <div class="modal-header">
                    <select class="form-select" id="author" name="author" required>
                        <option value="" disabled selected>Rank and name</option>
                        {% for user in users %}
                        <option value="{{ user.name }}" data-user-id="{{ user.id }}">{{ user.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Item Name</th>
                                <th scope="col">Quantity</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody id="send-item-container">
                            <tr class="send-item-row">
                                <td>
                                    <select class="form-select" name="item_name" required>
                                        <option value="" disabled selected>Select an item</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="number" name="quantity" class="form-control" required>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-outline-danger btn-sm send-remove-item" style="display: none;">Remove</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-outline-primary" id="send-add-item">Add Item</button>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Items</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    // Store current user balance to avoid re-fetching
    let currentUserBalance = {};

    function addSendItem() {
        const container = document.getElementById("send-item-container");
        const newRow = document.createElement("tr");
        newRow.className = "send-item-row";
        newRow.innerHTML = `
            <td>
                <select class="form-select" name="item_name" required>
                    <option value="" disabled selected>Select an item</option>
                </select>
            </td>
            <td>
                <input type="number" name="quantity" class="form-control" required>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm send-remove-item" style="display: none;">Remove</button>
            </td>
        `;
        container.appendChild(newRow);
        updateSendRemoveButtons();

        // Update only the new dropdown with current user's balance
        const newSelect = newRow.querySelector('select[name="item_name"]');
        updateSingleItemDropdown(newSelect, currentUserBalance);
    }

    function updateSendRemoveButtons() {
        const rows = document.querySelectorAll(".send-item-row");
        const removeButtons = document.querySelectorAll(".send-remove-item");
        removeButtons.forEach((btn, index) => {
            if (rows.length > 1) {
                btn.style.display = 'block';
                btn.onclick = function() {
                    rows[index].remove();
                    updateSendRemoveButtons();
                };
            } else {
                btn.style.display = 'none';
            }
        });
    }
    document.getElementById('send-add-item').addEventListener('click', addSendItem);

    function updateUserStock() {
        const authorSelect = document.getElementById('author');
        const selectedOption = authorSelect.options[authorSelect.selectedIndex];

        if (!selectedOption || !selectedOption.value) {
            return; // No user selected
        }

        // Get user ID from the selected option's data attribute or find it from users list
        const userName = selectedOption.value;
        const userId = selectedOption.dataset.userId;

        if (!userId) {
            console.error('User ID not found for selected user');
            return;
        }

        fetch(`/api/get_user/{{ stock_id }}/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentUserBalance = data.balance; // Store balance for future updates
                updateItemDropdowns(currentUserBalance);
            } else {
                console.error('Error fetching user balance:', data.message);
                currentUserBalance = {}; // Reset stored balance
                updateItemDropdowns({});
            }
        })
        .catch(error => {
            console.error('Error fetching user balance:', error);
            currentUserBalance = {}; // Reset stored balance
            updateItemDropdowns({});
        });
    }

    function updateSingleItemDropdown(select, userBalance) {
        // Clear existing options
        select.innerHTML = '<option value="" disabled selected>Select an item</option>';

        // Add options only for items the user has outstanding
        Object.keys(userBalance).forEach(itemName => {
            const quantity = userBalance[itemName];
            if (quantity > 0) { // Only show items with positive balance (user owes)
                const option = document.createElement('option');
                option.value = itemName;
                option.textContent = `${itemName} (${quantity} yet to return)`;
                select.appendChild(option);
            }
        });

        // If no items to return, show a message
        if (Object.keys(userBalance).length === 0 || Object.values(userBalance).every(qty => qty <= 0)) {
            const option = document.createElement('option');
            option.value = '';
            option.disabled = true;
            option.textContent = 'No items to return';
            select.appendChild(option);
        }
    }

    function updateItemDropdowns(userBalance) {
        const itemSelects = document.querySelectorAll('#send-item-container select[name="item_name"]');
        itemSelects.forEach(select => updateSingleItemDropdown(select, userBalance));
    }

    document.addEventListener('DOMContentLoaded', function() {
        const authorSelect = document.getElementById('author');
        const sendModal = document.getElementById('sendModal');

        authorSelect.addEventListener('change', updateUserStock);
        sendModal.addEventListener('shown.bs.modal', function() {
            const authorSelect = document.getElementById('author');
            authorSelect.selectedIndex = 0; // Reset to "Rank and name"
            currentUserBalance = {}; // Clear stored balance
            updateItemDropdowns({}); // Show empty dropdowns
        });
    });
</script>
