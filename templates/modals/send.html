<div class="modal fade" id="sendModal" tabindex="-1" aria-labelledby="sendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendModalLabel">Send Items to Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sendForm" method="POST">
                <div class="modal-body">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Item Name</th>
                                <th scope="col">Quantity</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody id="send-item-container">
                            <tr class="send-item-row">
                                <td>
                                    <select class="form-select" name="item_name" required>
                                        <option value="" disabled selected>Select an item</option>
                                        {% for item in inventory_items %}
                                        <option value="{{ item.name }}">{{ item.name }} ({{ item.on_loan }} on loan)</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <input type="number" name="quantity" class="form-control" required>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-outline-danger btn-sm send-remove-item" style="display: none;">Remove</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-outline-primary" id="send-add-item">Add Item</button>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Items</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    function addSendItem() {
        const container = document.getElementById("send-item-container");
        const newRow = document.createElement("tr");
        newRow.className = "send-item-row";
        newRow.innerHTML = `
            <td>
                <select class="form-select" name="item_name" required>
                    <option value="" disabled selected>Select an item</option>
                    {% for item in inventory_items %}
                    <option value="{{ item.name }}">{{ item.name }} ({{ item.on_loan }} on loan)</option>
                    {% endfor %}
                </select>
            </td>
            <td>
                <input type="number" name="quantity" class="form-control" required>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm send-remove-item" style="display: none;">Remove</button>
            </td>
        `;
        container.appendChild(newRow);
        updateSendRemoveButtons();
    }

    function updateSendRemoveButtons() {
        const rows = document.querySelectorAll(".send-item-row");
        const removeButtons = document.querySelectorAll(".send-remove-item");
        removeButtons.forEach((btn, index) => {
            if (rows.length > 1) {
                btn.style.display = 'block';
                btn.onclick = function() {
                    rows[index].remove();
                    updateSendRemoveButtons();
                };
            } else {
                btn.style.display = 'none';
            }
        });
    }

    document.getElementById('send-add-item').addEventListener('click', addSendItem);
</script>
