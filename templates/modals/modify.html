<div class="modal fade" id="modifyModal" tabindex="-1" aria-labelledby="sendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="drawModalLabel">Modify Items in Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="modifyForm" method="POST">
                <div class="modal-body">
                    <label class="form-label">Item Name</label>
                    <select class="form-select" id="item_name" name="item_name" required>
                        <option value="" disabled selected>Select an item</option>
                        {% for item in inventory_items %}
                        <option value="{{ item.name }}">{{ item.name }} ({{ item.quantity-item.on_loan }} in stock)</option>
                        {% endfor %}
                    </select>
                    <label class="form-label">New in-stock quantity</label>
                    <input type="number" id="quantity" name="quantity" class="form-control" required>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Modify Items</button>
                </div>
            </form>
        </div>
    </div>
</div>