<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Inventory Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="mb-3">Store Inventory Dashboard</h1>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Inventory Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Current Inventory</h5>
                    </div>
                    <div class="card-body">
                        {% if inventory_items %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th scope="col">Item</th>
                                            <th scope="col">On Loan</th>
                                            <th scope="col">In Stock</th>
                                            <th scope="col">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in inventory_items %}
                                            <tr>
                                                <td class="fw-medium">{{ item.name }}</td>
                                                <td>
                                                    <span class="badge bg-warning text-dark">{{ item.on_loan }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">{{ item.in_stock }}</span>
                                                </td>
                                                <td>{{ item.total }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-box-seam" style="font-size: 3rem; color: #6c757d;"></i>
                                </div>
                                <h5 class="text-muted">No inventory items found</h5>
                                <p class="text-muted">Start by creating your first transaction to add items to inventory.</p>
                                <a href="/setup" class="btn btn-primary">Create Transaction</a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Summary Cards -->
                {% if inventory_items %}
                    <div class="row mt-4 justify-content-center">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">{{ total_items }}</h5>
                                    <p class="card-text">Total Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">{{ total_in_stock }}</h5>
                                    <p class="card-text">In Stock</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">{{ total_on_loan }}</h5>
                                    <p class="card-text">On Loan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Draw Button -->
                <div class="row mt-3 justify-content-center">
                    <div class="col-md-4 col-sm-6 mb-3 text-center">
                        <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#drawModal">
                            Draw Items
                        </button>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3 text-center">
                        <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#sendModal">
                            Send Items
                        </button>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3 text-center">
                        <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#modifyModal">
                            Modify Items
                        </button>
                    </div>
                </div>

                <div class="row mt-4 justify-content-center">
                    <a href="{{ url_for('views.status', stock_id=stock_id) }}" class="btn btn-primary btn-lg">
                        View status
                    </a>
                    <a href="{{ url_for('views.setup') }}" class="btn btn-primary btn-lg">
                        View logs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Draw Modal -->
    {% include "modals/draw.html" %}

    <!-- Send Modal -->
    {% include "modals/send.html" %}

    <!-- Modify Modal -->
    {% include "modals/modify.html" %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const drawForm = document.getElementById('drawForm');
            const sendForm = document.getElementById('sendForm');
            const modifyForm = document.getElementById('modifyForm');
            const quantityInput = document.getElementById('quantity');
            const itemSelect = document.getElementById('item_name');

            // Form submission
            drawForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                fetch("/api/draw/{{ stock_id }}", {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert('An error occurred. Please try again.');
                });
            });

            sendForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                fetch("/api/send/{{ stock_id }}", {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert('An error occurred. Please try again.');
                });
            });

            modifyForm.addEventListener('submit', function (e) {
                e.preventDefault();

                const formData = new FormData(this);

                fetch("/api/modify/{{ stock_id }}", {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            location.reload();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert('An error occurred. Please try again.');
                    });
            });
        });
    </script>
</body>
</html>
