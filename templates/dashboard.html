<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Inventory Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="mb-3">Store Inventory Dashboard</h1>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Inventory Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Current Inventory</h5>
                    </div>
                    <div class="card-body">
                        {% if inventory_items %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th scope="col">Item</th>
                                            <th scope="col">On Loan</th>
                                            <th scope="col">In Stock</th>
                                            <th scope="col">Total</th>
                                            <!-- <th scope="col">Status</th> -->
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in inventory_items %}
                                            <tr>
                                                <td class="fw-medium">{{ item.name }}</td>
                                                <td>
                                                    <span class="badge bg-warning text-dark">{{ item.on_loan }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">{{ item.in_stock }}</span>
                                                </td>
                                                <td>{{ item.total }}</td>
                                                <!-- <td>
                                                    {% if item.in_stock == 0 %}
                                                        <span class="badge bg-danger">Out of Stock</span>
                                                    {% elif item.in_stock <= 5 %}
                                                        <span class="badge bg-warning text-dark">Low Stock</span>
                                                    {% else %}
                                                        <span class="badge bg-success">Available</span>
                                                    {% endif %}
                                                </td> -->
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-box-seam" style="font-size: 3rem; color: #6c757d;"></i>
                                </div>
                                <h5 class="text-muted">No inventory items found</h5>
                                <p class="text-muted">Start by creating your first transaction to add items to inventory.</p>
                                <a href="/setup" class="btn btn-primary">Create Transaction</a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Summary Cards -->
                {% if inventory_items %}
                    <div class="row mt-4 justify-content-center">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">{{ total_items }}</h5>
                                    <p class="card-text">Total Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">{{ total_in_stock }}</h5>
                                    <p class="card-text">In Stock</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">{{ total_on_loan }}</h5>
                                    <p class="card-text">On Loan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Draw Button -->
                <div class="text-center mt-5">
                    <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#drawModal">
                        Draw Items
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Draw Modal -->
    <div class="modal fade" id="drawModal" tabindex="-1" aria-labelledby="drawModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="drawModalLabel">Draw Items from Store</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="drawForm" action="/api/draw" method="POST">
                    {{ draw_form.hidden_tag() }}
                    <div class="modal-body">
                        <div class="mb-3">
                            {{ draw_form.item_name.label(class="form-label") }}
                            {{ draw_form.item_name(class="form-select", required=true) }}
                            {% if draw_form.item_name.errors %}
                                <div class="text-danger small">
                                    {% for error in draw_form.item_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ draw_form.quantity.label(class="form-label") }}
                            {{ draw_form.quantity(class="form-control", required=true) }}
                            {% if draw_form.quantity.errors %}
                                <div class="text-danger small">
                                    {% for error in draw_form.quantity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Draw Items</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Minimal JavaScript for form submission and real-time validation
        // document.addEventListener('DOMContentLoaded', function() {
        //     const form = document.getElementById('drawForm');
        //     const quantityInput = document.getElementById('quantity');
        //     const itemSelect = document.getElementById('item_name');

        //     // Real-time quantity validation
        //     function validateQuantity() {
        //         if (!quantityInput.value || !itemSelect.value) return;

        //         const formData = new FormData();
        //         formData.append('item_name', itemSelect.value);
        //         formData.append('quantity', quantityInput.value);
        //         formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);

        //         fetch('/api/validate-quantity', {
        //             method: 'POST',
        //             body: formData
        //         })
        //         .then(response => response.json())
        //         .then(data => {
        //             const quantityGroup = quantityInput.closest('.mb-3');
        //             let errorDiv = quantityGroup.querySelector('.validation-error');

        //             // Remove existing error message
        //             if (errorDiv) errorDiv.remove();

        //             if (!data.valid) {
        //                 // Add error message
        //                 errorDiv = document.createElement('div');
        //                 errorDiv.className = 'text-danger small validation-error';
        //                 errorDiv.textContent = data.message;
        //                 quantityGroup.appendChild(errorDiv);
        //             }
        //         })
        //         .catch(error => console.log('Validation error:', error));
        //     }

        //     // Add event listeners for real-time validation
        //     quantityInput.addEventListener('input', validateQuantity);
        //     itemSelect.addEventListener('change', validateQuantity);

        //     // Form submission
        //     form.addEventListener('submit', function(e) {
        //         e.preventDefault();

        //         const formData = new FormData(this);

        //         fetch('/api/draw', {
        //             method: 'POST',
        //             body: formData
        //         })
        //         .then(response => response.json())
        //         .then(data => {
        //             if (data.success) {
        //                 // Show success and refresh page
        //                 alert(data.message);
        //                 location.reload();
        //             } else {
        //                 // Show error message
        //                 alert(data.message);
        //             }
        //         })
        //         .catch(error => {
        //             alert('An error occurred. Please try again.');
        //         });
        //     });
        // });
    </script>
</body>
</html>
