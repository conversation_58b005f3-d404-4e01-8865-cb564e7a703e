<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Inventory Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="mb-3">Store Inventory Dashboard</h1>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="/setup" class="btn btn-primary">Create Transaction</a>
                        <a href="/dashboard" class="btn btn-success">View Dashboard</a>
                        <a href="/" class="btn btn-outline-secondary">Home</a>
                    </div>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Inventory Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Current Inventory</h5>
                    </div>
                    <div class="card-body">
                        {% if inventory_items %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th scope="col">Item</th>
                                            <th scope="col">On Loan</th>
                                            <th scope="col">In Stock</th>
                                            <th scope="col">Total</th>
                                            <!-- <th scope="col">Status</th> -->
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in inventory_items %}
                                            <tr>
                                                <td class="fw-medium">{{ item.name }}</td>
                                                <td>
                                                    <span class="badge bg-warning text-dark">{{ item.on_loan }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">{{ item.in_stock }}</span>
                                                </td>
                                                <td>{{ item.total }}</td>
                                                <!-- <td>
                                                    {% if item.in_stock == 0 %}
                                                        <span class="badge bg-danger">Out of Stock</span>
                                                    {% elif item.in_stock <= 5 %}
                                                        <span class="badge bg-warning text-dark">Low Stock</span>
                                                    {% else %}
                                                        <span class="badge bg-success">Available</span>
                                                    {% endif %}
                                                </td> -->
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-box-seam" style="font-size: 3rem; color: #6c757d;"></i>
                                </div>
                                <h5 class="text-muted">No inventory items found</h5>
                                <p class="text-muted">Start by creating your first transaction to add items to inventory.</p>
                                <a href="/setup" class="btn btn-primary">Create Transaction</a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Summary Cards -->
                {% if inventory_items %}
                    <div class="row mt-4 justify-content-center">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">{{ total_items }}</h5>
                                    <p class="card-text">Total Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">{{ total_in_stock }}</h5>
                                    <p class="card-text">In Stock</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">{{ total_on_loan }}</h5>
                                    <p class="card-text">On Loan</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">{{ low_stock_count }}</h5>
                                    <p class="card-text">Low Stock Items</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
