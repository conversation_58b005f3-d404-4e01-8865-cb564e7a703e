<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Transaction - Store Inventory</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="mb-4">Create Transaction</h1>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div id="items-container">
                        <div class="row mb-3 item-row">
                            <div class="col-md-6">
                                {{ form.item_name.label(class="form-label") }}
                                {{ form.item_name(class="form-control") }}
                                {% if form.item_name.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.item_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                {{ form.quantity.label(class="form-label") }}
                                {{ form.quantity(class="form-control") }}
                                {% if form.quantity.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.quantity.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-danger btn-sm remove-item" style="display: none;">Remove</button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" id="add-item">Add Item</button>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">Create Transaction</button>
                        <a href="/" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function addItem() {
            const container = document.getElementById('items-container');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-3 item-row';
            newRow.innerHTML = `
                <div class="col-md-6">
                    {{ form.item_name.label(class="form-label") }}
                    {{ form.item_name(class="form-control") }}
                    {% if form.item_name.errors %}
                        <div class="text-danger small">
                            {% for error in form.item_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.quantity.label(class="form-label") }}
                    {{ form.quantity(class="form-control") }}
                    {% if form.quantity.errors %}
                        <div class="text-danger small">
                            {% for error in form.quantity.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-item" style="display: none;">Remove</button>
                </div>
            `;
            container.appendChild(newRow);
            updateRemoveButtons();
        }

        function updateRemoveButtons() {
            const rows = document.querySelectorAll('.item-row');
            const removeButtons = document.querySelectorAll('.remove-item');
            
            removeButtons.forEach((btn, index) => {
                if (rows.length > 1) {
                    btn.style.display = 'block';
                    btn.onclick = function() {
                        rows[index].remove();
                        updateRemoveButtons();
                    };
                } else {
                    btn.style.display = 'none';
                }
            });
        }

        document.getElementById('add-item').addEventListener('click', addItem);
    </script>
</body>
</html>
