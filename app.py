import logging
import os
from flask import Flask, render_template, session
from models import db, migrate

def create_app():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    app = Flask(__name__)

    # --- App Config ---
    app.config['SECRET_KEY'] = os.urandom(16)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(app.instance_path, 'database.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = True

    # --- Initialise database ---
    db.init_app(app)
    migrate.init_app(app, db)
    os.makedirs(app.instance_path, exist_ok=True)
    
    # --- Register routes ---
    @app.route("/")
    def index():
        return render_template("index.html")

    from views.routes import views_bp
    from api.routes import api_bp
    app.register_blueprint(views_bp)
    app.register_blueprint(api_bp, url_prefix="/api")

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, port=8000)