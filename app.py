import os
from flask import Flask, render_template

def create_app():
    app = Flask(__name__)
    app.config['SECRET_KEY'] = os.urandom(16)

    @app.route("/")
    def index():
        return render_template("index.html")

    from views.routes import views_bp
    from api.routes import api_bp
    app.register_blueprint(views_bp)
    app.register_blueprint(api_bp, url_prefix="/api")

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, port=5000)