from flask import Blueprint, render_template, flash, redirect, url_for, jsonify, current_app
from models import db, Item
from views.forms import DrawForm

api_bp = Blueprint("api", __name__, template_folder="templates")

@api_bp.route("/draw", methods=["POST"])
def draw():
    try:
        # Create form instance with current request data
        form = DrawForm()

        # Populate form choices (needed for validation)
        # Get available items for the dropdown
        all_items = Item.query.all()
        item_dict = {}

        # Group items by name and sum quantities
        for item in all_items:
            if item.name in item_dict:
                item_dict[item.name] += item.quantity
            else:
                item_dict[item.name] = item.quantity

        # Create choices for the form
        form.item_name.choices = [(name, f"{name} ({qty} available)") for name, qty in item_dict.items() if qty > 0]

        # Validate the form using WTForms validators
        if not form.validate():
            # Return validation errors
            errors = []
            for field, field_errors in form.errors.items():
                for error in field_errors:
                    errors.append(f"{field}: {error}")

            return jsonify({
                'success': False,
                'message': '; '.join(errors)
            }), 400

        # If validation passes, process the draw
        item_name = form.item_name.data
        quantity = form.quantity.data

        # Find the most recent stock entry for this item
        item = Item.query.filter_by(name=item_name).order_by(Item.id.desc()).first()

        # Update the item quantity (draw items from stock)
        item.quantity -= quantity
        db.session.commit()

        current_app.logger.info(f"Drew {quantity} of {item_name}. Remaining: {item.quantity}")

        return jsonify({
            'success': True,
            'message': f'Successfully drew {quantity} {item_name}(s). {item.quantity} remaining in stock.'
        })

    except Exception as e:
        current_app.logger.error(f"Error drawing items: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'An error occurred while drawing items.'
        }), 500

@api_bp.route("/validate-quantity", methods=["POST"])
def validate_quantity():
    """Endpoint for real-time quantity validation"""
    try:
        # Create form instance for validation
        form = DrawForm()

        # Populate form choices
        all_items = Item.query.all()
        item_dict = {}

        # Group items by name and sum quantities
        for item in all_items:
            if item.name in item_dict:
                item_dict[item.name] += item.quantity
            else:
                item_dict[item.name] = item.quantity

        form.item_name.choices = [(name, f"{name} ({qty} available)") for name, qty in item_dict.items() if qty > 0]

        # Validate only the quantity field
        if form.validate():
            return jsonify({
                'valid': True,
                'message': 'Quantity is valid'
            })
        else:
            # Return quantity-specific errors
            quantity_errors = form.quantity.errors
            if quantity_errors:
                return jsonify({
                    'valid': False,
                    'message': quantity_errors[0]
                })
            else:
                return jsonify({
                    'valid': True,
                    'message': 'Quantity is valid'
                })

    except Exception as e:
        current_app.logger.error(f"Error validating quantity: {e}")
        return jsonify({
            'valid': False,
            'message': 'Validation error occurred'
        }), 500

@api_bp.route("/send", methods=["GET", "POST"])
def send():
    return "Hello World!"

@api_bp.route("/modify", methods=["GET", "POST"])
def modify():
    return "Hello World!"