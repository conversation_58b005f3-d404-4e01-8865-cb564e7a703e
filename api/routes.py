from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from models import db, Item

api_bp = Blueprint("api", __name__, template_folder="templates")

@api_bp.route("/draw", methods=["POST"])
def draw():
    try:
        item_name = request.form.get('item_name')
        quantity = int(request.form.get('quantity', 0))

        if not item_name or quantity <= 0:
            return jsonify({
                'success': False,
                'message': 'Please provide valid item name and quantity.'
            }), 400

        # Find the most recent stock entry for this item
        item = Item.query.filter_by(name=item_name).order_by(Item.id.desc()).first()

        if not item:
            return jsonify({
                'success': False,
                'message': f'Item "{item_name}" not found in inventory.'
            }), 404

        # Check if enough quantity is available
        if item.quantity < quantity:
            return jsonify({
                'success': False,
                'message': f'Insufficient stock. Only {item.quantity} available.'
            }), 400

        # Update the item quantity (draw items from stock)
        item.quantity -= quantity
        db.session.commit()

        current_app.logger.info(f"Drew {quantity} of {item_name}. Remaining: {item.quantity}")

        return jsonify({
            'success': True,
            'message': f'Successfully drew {quantity} {item_name}(s). {item.quantity} remaining in stock.'
        })

    except ValueError:
        return jsonify({
            'success': False,
            'message': 'Invalid quantity provided.'
        }), 400
    except Exception as e:
        current_app.logger.error(f"Error drawing items: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'An error occurred while drawing items.'
        }), 500

@api_bp.route("/send", methods=["GET", "POST"])
def send():
    return "Hello World!"

@api_bp.route("/modify", methods=["GET", "POST"])
def modify():
    return "Hello World!"