from flask import Blueprint, jsonify, current_app, request
from models import db, Item, Event, User
import json
from api.validators import validate_draw, validate_send, validate_modify

api_bp = Blueprint("api", __name__, template_folder="templates")

def get_balance(user_id, stock_id):
    user = User.query.filter_by(id=user_id, stock_id=stock_id).first()
    if not user:
        return False
    
    events = Event.query.filter_by(stock_id=stock_id, user_id=user_id).all()
    balance = {} # Net count of stuff the user has drawn

    for event in events:
        items = json.loads(event.items)
        for item in items:
            if item in balance:
                balance[item] += items[item]*(1 if event.event_type=="draw" else -1)
            else:
                balance[item] = items[item]*(1 if event.event_type=="draw" else -1)

    return balance

@api_bp.route("/draw/<int:stock_id>", methods=["POST"])
def draw(stock_id):
    item_names = request.form.getlist("item_name")
    quantities = request.form.getlist("quantity")
    author = request.form.get("author").strip().upper()

    item_qty = {} # Duplicate handling
    for item, quantity in zip(item_names, quantities):
        if item in item_qty:
            item_qty[item] += int(quantity)
        else:
            item_qty[item] = int(quantity)

    errors = []
    for item_name, quantity in item_qty.items():
        item = Item.query.filter_by(name=item_name, stock_id=stock_id).order_by(Item.id.desc()).first()
        quantity = int(quantity)

        response = validate_draw(item, quantity)
        if response is not None:  # validate_draw returns None on success, jsonify response on error
            response = json.loads(response.data)
            if not response["success"]:
                errors.append(response["message"])

        if len(errors): # We want to catch all errors, but prevent the transaction from going through as long as errors exist
            continue

        item.on_loan += quantity
        db.session.commit()
        current_app.logger.info(f"Successfully drew {quantity} {item_name}(s) from stock {stock_id}.")

    if len(errors):
        return jsonify({"success": False, "message": f"Failed to draw items.\n{errors}"})
    
    # Log event
    user = User.query.filter_by(name=author, stock_id=stock_id).first()
    if not user:
        user = User(name=author, stock_id=stock_id)
        db.session.add(user)
        db.session.commit()

    event = Event(stock_id=stock_id,
                  items=json.dumps(item_qty),
                  event_type="draw",
                  user_id=user.id)
    db.session.add(event)
    db.session.commit()

    return jsonify({"success": True, 
                    "message": f"{author} successfully drew {len(item_qty)} items.\n{item_qty}"})

@api_bp.route("/send/<int:stock_id>", methods=["POST"])
def send(stock_id):
    item_names = request.form.getlist("item_name")
    quantities = request.form.getlist("quantity")
    author = request.form.get("author").strip().upper()
    user = User.query.filter_by(name=author, stock_id=stock_id).first()

    item_qty = {} # Duplicate handling
    for item, quantity in zip(item_names, quantities):
        if item in item_qty:
            item_qty[item] += int(quantity)
        else:
            item_qty[item] = int(quantity)

    errors = []
    balance = get_balance(user.id, stock_id)
    for item_name, quantity in item_qty.items():
        item = Item.query.filter_by(name=item_name, stock_id=stock_id).order_by(Item.id.desc()).first()
        quantity = int(quantity)

        response = validate_send(item, quantity, balance)
        if response is not None:  # validate_send returns None on success, jsonify response on error
            response = json.loads(response.data)
            if not response["success"]:
                errors.append(response["message"])

        if len(errors): # see draw function
            continue

        item.on_loan -= quantity
        db.session.commit()
        current_app.logger.info(f"Successfully sent {quantity} {item_name}(s) from stock {stock_id}.")

    if len(errors):
        return jsonify({"success": False, "message": f"Failed to send items.\n{errors}"})
    
    # Log event
    user = User.query.filter_by(name=author, stock_id=stock_id).first()
    if not user:
        user = User(name=author, stock_id=stock_id)
        db.session.add(user)
        db.session.commit()

    event = Event(stock_id=stock_id,
                  items=json.dumps(item_qty),
                  event_type="send",
                  user_id=user.id)
    db.session.add(event)
    db.session.commit()

    return jsonify({"success": True,
                    "message": f"{author} successfully sent {len(item_qty)} items.\n{item_qty}"})

@api_bp.route("/modify/<int:stock_id>", methods=["GET", "POST"])
def modify(stock_id):
    """
    Modifies the quantity of an item currently in stock.
    Note: This does not affect the quantity of the item on loan.
    """
    item_name = request.form.get("item_name")
    quantity = int(request.form.get("quantity"))
    item = Item.query.filter_by(name=item_name, stock_id=stock_id).order_by(Item.id.desc()).first()

    response = validate_modify(item, quantity)
    if response is not None:
        return response

    item.quantity = quantity + item.on_loan 
    db.session.commit()

    # Log event
    event = Event(stock_id=stock_id,
                  items=json.dumps({item_name: quantity}),
                  event_type="modify",
                  user_id=1) # Admin user
    db.session.add(event)
    db.session.commit()

    current_app.logger.info(f"Successfully modified {item_name} to {quantity} in stock {stock_id}.")
    return jsonify({"success": True, "message": f"Successfully modified {item_name} to {quantity}."})

@api_bp.route("/get_user/<int:stock_id>/<int:user_id>", methods=["POST", "GET"])
def get_user(stock_id, user_id):
    user = User.query.filter_by(id=user_id, stock_id=stock_id).first()
    if not user:
        return jsonify({"success": False, "message": "User not found."})
    
    balance = get_balance(user_id, stock_id)
                                             
    return jsonify({"success": True, "user": user.name, "balance": balance})