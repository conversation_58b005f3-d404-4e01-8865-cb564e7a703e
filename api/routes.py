from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app

api_bp = Blueprint("api", __name__, template_folder="templates")

@api_bp.route("/draw", methods=["GET", "POST"])
def draw():
    return "Hello World!"

@api_bp.route("/send", methods=["GET", "POST"])
def send():
    return "Hello World!"

@api_bp.route("/modify", methods=["GET", "POST"])
def modify():
    return "Hello World!"