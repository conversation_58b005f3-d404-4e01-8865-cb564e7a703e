from flask import current_app, jsonify

def validate_draw(item, quantity):
    if not item:
        current_app.logger.error(f"Item not found in inventory.")
        return jsonify({"success": False, "message": f"Item not found in inventory."})
    if quantity <= 0:
        current_app.logger.error(f"Invalid quantity for item '{item.name}'. Requested: {quantity}")
        return jsonify({"success": False, "message": f"Invalid quantity. Please enter a positive number."})

    in_stock = item.quantity - item.on_loan
    if quantity > in_stock:
        current_app.logger.error(f"Insufficient quantity for item '{item.name}'. Requested: {quantity}, Available: {in_stock}")
        return jsonify({"success": False, "message": f"Insufficient quantity for item '{item.name}'. Requested: {quantity}, Available: {in_stock}"})

    return None 

def validate_send(item, quantity, balance):
    if not item:
        current_app.logger.error(f"Item not found in inventory.")
        return jsonify({"success": False, "message": f"Item not found in inventory."})
    if quantity <= 0:
        current_app.logger.error(f"Invalid quantity for item '{item.name}'. Requested: {quantity}")
        return jsonify({"success": False, "message": f"Invalid quantity. Please enter a positive number."})
    if item.name in balance and quantity > balance[item.name]:
        current_app.logger.error(f"Unable to send more items than drawn for item '{item.name}'. Requested: {quantity}, Drawn: {balance[item.name]}")
        return jsonify({"success": False, "message": f"Unable to send more items than drawn for item '{item.name}'. Requested: {quantity}, Drawn: {balance[item.name]}"})
    if item.name not in balance:
        current_app.logger.error(f"Unable to send item '{item.name}' that has not been drawn.")
        return jsonify({"success": False, "message": f"Unable to send item '{item.name}' that has not been drawn."})
    
    return None  

def validate_modify(item, quantity):
    if not item:
        current_app.logger.error(f"Item not found in inventory.")
        return jsonify({"success": False, "message": f"Item not found in inventory."})
    if quantity < 0:
        current_app.logger.error(f"Invalid quantity for item '{item.name}'. Requested: {quantity}")
        return jsonify({"success": False, "message": f"Invalid quantity. Please enter a positive number."})
    if quantity == item.quantity - item.on_loan:
        current_app.logger.error(f"New quantity is the same as current quantity for item '{item.name}'. Requested: {quantity}, Current: {item.quantity}")
        return jsonify({"success": False, "message": f"New quantity is the same as current quantity for item '{item.name}'. Requested: {quantity}, Current: {item.quantity}"})

    return None  