from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from setup.forms import SetupForm

setup_bp = Blueprint("setup", __name__, template_folder="templates")

@setup_bp.route("/setup", methods=["GET", "POST"])
def setup():
    form = SetupForm()

    if request.method == "POST":
        # Process the transaction form using raw form data for multiple items
        items = []
        item_names = request.form.getlist("item_name")
        quantities = request.form.getlist("quantity")

        # Combine item names and quantities
        for name, qty in zip(item_names, quantities):
            if name.strip() and qty.strip():
                try:
                    quantity = int(qty)
                    items.append({"name": name.strip(), "quantity": quantity})
                except ValueError:
                    flash(f"Invalid quantity for {name}: {qty}", "error")
                    return render_template("setup.html", form=form)

        if not items:
            flash("Please add at least one item with valid quantity.", "error")
            return render_template("setup.html", form=form)

        flash(f"Transaction created with {len(items)} items!", "success")
        return jsonify(items)

    return render_template("setup.html", form=form)