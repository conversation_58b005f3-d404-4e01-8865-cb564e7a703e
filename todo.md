# TODO
1. Log all ops into some dict that is exported afterwards
3. Display outstanding and completed orders
## Write tests
4. validate total is invariant by draw/send
5. validate all routes.status_code==200
6. validate send event does not exceed corresponding draw

# 050725
[x] Batch operations
[] Select corresponding draw event when sending something

# 040725
[x] Learn databases again (theory)
[x] Implement db models
[x] api/draw 
[x] api/send 
[x] api/modify

# 010725
[x] Create transaction
[x] Initialise inventory