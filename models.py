from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import json

db = SQLAlchemy()
migrate = Migrate()

class JSONEncodedDict(db.TypeDecorator):
    impl = db.Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            value = json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            value = json.loads(value)
        return value

class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nric = db.Column(db.String(12), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    allocations = db.relationship('Stock', backref='admin', lazy=True)

    def __repr__(self):
        return f"<Admin {self.name}>"

class Stock(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(500), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    admin_id = db.Column(db.Integer, db.ForeignKey("admin.id"), nullable=False)
    events = db.relationship("Event", backref="stock", lazy=True)
    items = db.relationship("Item", backref="stock", lazy=True)

    def __repr__(self):
        return f"<Stock {self.id}, created by {self.admin.name}>"

class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    on_loan = db.Column(db.Integer, default=0)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)

    def __repr__(self):
        return f"<Item {self.name}>"
    
class Event(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)
    items = db.Column(JSONEncodedDict, nullable=False)
    event_type = db.Column(db.String(10), nullable=False) # "draw" or "send" or "modify"
    timestamp = db.Column(db.DateTime, default=db.func.current_timestamp())

    def __repr__(self):
        return f"<Event {self.id}, {self.event_type} {self.quantity} {self.items}>"