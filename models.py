from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy()
migrate = Migrate()

class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nric = db.Column(db.String(12), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    allocations = db.relationship('Stock', backref='admin', lazy=True)

    def __repr__(self):
        return f"<Admin {self.name}>"

class Stock(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(500), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    admin_id = db.Column(db.Integer, db.ForeignKey("admin.id"), nullable=False)

    items = db.relationship("Item", backref="stock", lazy=True)

    def __repr__(self):
        return f"<Stock {self.id}, created by {self.admin.name}>"

class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    stock_id = db.Column(db.Integer, db.ForeignKey("stock.id"), nullable=False)

    def __repr__(self):
        return f"<Item {self.name}>"