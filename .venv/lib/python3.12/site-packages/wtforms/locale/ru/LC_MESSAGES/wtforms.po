# Russian translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.3\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2024-04-26 21:07+0000\n"
"Last-Translator: \"Sophie Sh.\" <<EMAIL>>\n"
"Language-Team: Russian <https://hosted.weblate.org/projects/wtforms/wtforms/"
"ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Weblate 5.5.1\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Неправильное имя поля '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Поле должно совпадать с %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Значение должно содержать не менее %(min)d символа."
msgstr[1] "Значение должно содержать не менее %(min)d символов."
msgstr[2] "Значение должно содержать не менее %(min)d символов."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Значение не должно содержать более %(max)d символа."
msgstr[1] "Значение не должно содержать более %(max)d символов."
msgstr[2] "Значение не должно содержать более %(max)d символов."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Значение должно быть точно %(max)d символа."
msgstr[1] "Значение должно быть %(max)d символов."
msgstr[2] "Значение должно быть %(max)d символов."

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Значение должно содержать от %(min)d до %(max)d символов."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Число должно быть больше %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Число должно быть меньше %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Значение должно быть между %(min)s и %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Обязательное поле."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Некорректный ввод."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Неверный адрес электронной почты."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Неверный IP адрес."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Неверный MAC адрес."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "Неверный URL."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "Неверный UUID."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Неверное значение, должно быть одним из %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Неверное значение, не должно быть одним из %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited."
msgstr "Данное поле не может быть изменено."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr "Данное поле отключено и не может быть изменено."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Неверный CSRF токен."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "CSRF токен отсутствует."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Ошибка CSRF."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF токен просрочен."

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Неверный вариант: невозможно преобразовать."

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr "Выбор не может быть None"

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Неверный вариант."

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Неверный вариант(варианты): одно или несколько значений невозможно "
"преобразовать."

#: src/wtforms/fields/choices.py:214
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' - неверный вариант для этого поля."
msgstr[1] "'%(value)s' - неверный вариант для этого поля."
msgstr[2] "'%(value)s' - неверный вариант для этого поля."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Неверное значение даты и времени."

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Неверное значение даты."

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr "Неверное значение времени."

#: src/wtforms/fields/datetime.py:148
msgid "Not a valid week value."
msgstr "Неверное значение недели."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Неверное целое число."

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Неверное десятичное число."

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Неверное десятичное число."
